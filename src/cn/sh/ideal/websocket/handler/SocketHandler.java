//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fern<PERSON>lower decompiler)
//

package cn.sh.ideal.websocket.handler;

import cn.sh.ideal.websocket.RequestType;
import cn.sh.ideal.websocket.model.DubboMessageType;
import cn.sh.ideal.websocket.model.Message;
import cn.sh.ideal.websocket.model.MessageType;
import cn.sh.ideal.websocket.model.MethodMessageType;
import cn.sh.ideal.websocket.model.UrlMessageType;
import cn.sh.ideal.websocket.model.MessageType.SERVICE_TYPE;
import cn.sh.ideal.websocket.model.UrlMessageType.Flag;
import cn.sh.ideal.websocket.util.MessageUtil;
import com.alibaba.fastjson.JSONObject;
import java.io.IOException;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Timer;
import java.util.TimerTask;

import org.apache.log4j.Logger;
import org.springframework.util.StringUtils;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.adapter.standard.StandardWebSocketSession;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;

public class SocketHandler extends AbstractWebSocketHandler {
    private static Logger logger = Logger.getLogger(SocketHandler.class);
    private static SocketHandler instance;
    static Map<String, String> relationMappings = new HashMap();
    static Map<String, MessageType> messageTypes = new HashMap();
    static Map<String, WebSocketSession> sessionRegistry = new HashMap(); // Replace MessageRepeatTask session storage
    public static final String YES = "1";
    public static final String NO = "0";
    static Map<WebSocketSession, Calendar> sessionLifeMap = new HashMap();
    private static final String CONFIG_FILE_NAME = "messageTypes.properties";
    private static WebSocketSessionListener webSocketSessionListener = new WebSocketSessionListener();
    private static MessageFilter messageFilter;
    private static CloseStatus activeCloseStatus = new CloseStatus(2000);
    private static final String LIFE_IDENTIF = "#";

    static {
        Properties properties = new Properties();

        try {
            properties.load(SocketHandler.class.getClassLoader().getResourceAsStream("messageTypes.properties"));

            for(Map.Entry<Object, Object> entry : properties.entrySet()) {
                String key = String.valueOf(entry.getKey());
                messageTypes.put(key, new UrlMessageType(String.valueOf(entry.getValue()), Flag.NO, key, SERVICE_TYPE.EXTERNAL.getValue()));
            }

            (new Timer()).schedule(new TimerTask() {
                public void run() {
                    // 心跳任务执行，改为debug级别避免频繁日志
                    SocketHandler.logger.debug("execute timer task!");
                    synchronized(SocketHandler.sessionLifeMap) {
                        try {
                            List<WebSocketSession> deleteList = new ArrayList();
                            Calendar currentTime = Calendar.getInstance();
                            currentTime.add(13, -10);

                            for(Map.Entry<WebSocketSession, Calendar> entry : SocketHandler.sessionLifeMap.entrySet()) {
                                WebSocketSession session = (WebSocketSession)entry.getKey();
                                Calendar life = (Calendar)entry.getValue();
                                if (!session.isOpen()) {
                                    deleteList.add(session);
                                } else if (currentTime.after(life)) {
                                    SocketHandler.logger.info("心跳停止关闭会话,ID[" + session.getId() + "]");
                                    session.close();
                                    deleteList.add(session);
                                } else {
                                    session.sendMessage(new TextMessage("#"));
                                }
                            }

                            for(WebSocketSession s : deleteList) {
                                SocketHandler.sessionLifeMap.remove(s);
                            }
                        } catch (Exception e) {
                            SocketHandler.logger.error("心跳任务错误", e);
                        }

                    }
                }
            }, 5000L, 5000L);
        } catch (IOException var5) {
            logger.error("config file not found!");
        }

    }

    private SocketHandler() {
    }

    public static SocketHandler getInstance() {
        if (instance == null) {
            synchronized(SocketHandler.class) {
                if (instance == null) {
                    instance = new SocketHandler();
                }
            }
        }

        return instance;
    }

    protected void handleTextMessage(WebSocketSession session, TextMessage message) {
        String content = (String)message.getPayload();

        try {
            if ("#".equals(content)) {
                synchronized(sessionLifeMap) {
                    sessionLifeMap.put(session, Calendar.getInstance());
                    return;
                }
            }

            // 只记录消息类型和长度，避免大量日志
            Message tempMessage = (Message)JSONObject.parseObject(content, Message.class);
            logger.debug("Received message type: " + tempMessage.getMessageType() + ", length: " + content.length());
            Message requestMessage = tempMessage; // 重用已解析的消息对象
            Object result = null;
            if (RequestType.RECEIPT.getCode().equals(requestMessage.getRequestType())) {
                // Receipt messages are no longer processed since retry mechanism is removed
                logger.debug("Receipt message received but ignored, id [" + requestMessage.getServerMessageId() + "]");
                return;
            }

            if (RequestType.IDENTIFY.getCode().equals(requestMessage.getRequestType())) {
                this.setSession(session, requestMessage.getClientId());
                return;
            }

            if (messageFilter != null && !messageFilter.filter(requestMessage)) {
                logger.info("message be prevented filter false ");
                return;
            }

            Message replyMessage = new Message();
            if ("1".equals(requestMessage.getIsReceipt())) {
                replyMessage.setIsReceipt("1");
                replyMessage.setClientId(requestMessage.getClientId());
                replyMessage.setClientMessageId(requestMessage.getClientMessageId());
                replyMessage.setServerMessageId(StringUtils.isEmpty(replyMessage.getServerMessageId()) ? (StringUtils.isEmpty(replyMessage.getClientMessageId()) ? MessageUtil.getMessageID() : replyMessage.getClientMessageId()) : replyMessage.getServerMessageId());
                replyMessage.setRequestType(RequestType.COMMON.getCode());
            }

            MessageType messageType = (MessageType)messageTypes.get(requestMessage.getMessageType());
            if (messageType == null) {
                logger.warn("message type [" + requestMessage.getMessageType() + "] not found");
                return;
            }

            result = requestMessage.getContent() == null ? messageType.invoke() : messageType.invoke(requestMessage.getContent());
            // 只记录响应状态，避免大数据量日志
            if (result != null) {
                String resultStr = result.toString();
                if (resultStr.length() > 200) {
                    logger.debug("Response: [Large data " + resultStr.length() + " chars] " + resultStr.substring(0, 200) + "...");
                } else {
                    logger.debug("Response: " + resultStr);
                }
            } else {
                logger.debug("Response: null");
            }
            if ("1".equals(requestMessage.getIsReceipt())) {
                if (result != null) {
                    replyMessage.setContent(JSONObject.parseObject(result.toString()));
                }

                send(replyMessage, session);
            }
        } catch (Exception e) {
            logger.error(e);
        }

    }

    public void setSession(WebSocketSession session, Object clientId) {
        if (relationMappings.containsKey(clientId)) {
            logger.warn("clientId existed! old session id[" + (String)relationMappings.get(clientId) + "], new session id [" + session.getId() + "]");
        }

        relationMappings.put(String.valueOf(clientId), session.getId());
    }

    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        // Register session in our simple registry
        sessionRegistry.put(session.getId(), session);
        logger.debug("afterConnectionEstablished, sessionId = " + session.getId());
        if (session.getClass() == StandardWebSocketSession.class) {
            synchronized(sessionLifeMap) {
                sessionLifeMap.put(session, Calendar.getInstance());
            }
        }

        Message replyMessage = new Message();
        replyMessage.setIsReceipt("0");
        replyMessage.setClientId(session.getId());
        replyMessage.setRequestType(RequestType.SESSIONID.getCode());
        send(replyMessage, session);
        webSocketSessionListener.onCreate(session);
    }

    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        logger.debug("SocketHandler.afterConnectionClosed, CloseStatus = " + status);
        String sessionId = session.getId();
        String clientId = null;

        // Remove session from registry
        sessionRegistry.remove(sessionId);

        for(Map.Entry<String, String> entry : relationMappings.entrySet()) {
            if (sessionId.equals(entry.getValue())) {
                clientId = (String)entry.getKey();
                break;
            }
        }

        logger.debug("SocketHandler.afterConnectionClosed, sessionId = " + sessionId + " clientId =" + clientId);
        if (!StringUtils.isEmpty(clientId)) {
            relationMappings.remove(clientId);
        }

        if (status.getCode() != activeCloseStatus.getCode()) {
            webSocketSessionListener.onClose(session, clientId);
        }

        logger.info("afterConnectionClosed");
    }

    public static void send(Message message) throws Exception {
        send(message, (WebSocketSession)null);
    }

    public static void send(Message message, WebSocketSession session) throws Exception {
        if (session == null) {
            String clientId = message.getClientId();
            // Find session by clientId from relationMappings
            if (relationMappings.containsKey(clientId)) {
                String sessionId = relationMappings.get(clientId);
                session = sessionRegistry.get(sessionId);
            }

            // Try direct lookup by clientId as sessionId
            if (session == null && sessionRegistry.containsKey(clientId)) {
                session = sessionRegistry.get(clientId);
            }

            if (session == null) {
                throw new Exception("web socket session not found, id [" + clientId + "]");
            }
        }

        if (!session.isOpen()) {
            throw new Exception("session closed");
        } else {
            if (StringUtils.isEmpty(message.getServerMessageId())) {
                message.setServerMessageId(MessageUtil.getMessageID());
            }

            if (StringUtils.isEmpty(message.getRequestType())) {
                message.setRequestType(RequestType.PUSH.getCode());
            }

            if (StringUtils.isEmpty(message.getIsReceipt())) {
                message.setIsReceipt("0"); // No receipt needed since no retry mechanism
            }

            session.sendMessage(new TextMessage(JSONObject.toJSONString(message)));
        }
    }

    public static void registerMessageType(String messageType, Object obj, String methodName, Class<?> parameterTypes) throws Exception {
        Method method = obj.getClass().getMethod(methodName, parameterTypes);
        messageTypes.put(messageType, new MethodMessageType(obj, method, parameterTypes, messageType, SERVICE_TYPE.LOCAL_METHOD.getValue()));
    }

    public static void registerMessageType(String messageType, String url) throws Exception {
        if (StringUtils.isEmpty(url)) {
            throw new Exception("url is null");
        } else {
            messageTypes.put(messageType, new UrlMessageType(url, Flag.NO, messageType, SERVICE_TYPE.EXTERNAL.getValue()));
        }
    }

    public static void registerMessageType(String messageType, String url, String protocol) throws Exception {
        if (StringUtils.isEmpty(url)) {
            throw new Exception("url is null");
        } else {
            messageTypes.put(messageType, new UrlMessageType(url, Flag.YES, messageType, protocol));
        }
    }

    public static void registerMessageType(String messageType, String serviceName, String serviceClassName, String methodName, String requestClassName) throws Exception {
        messageTypes.put(messageType, new DubboMessageType(serviceName, serviceClassName, methodName, requestClassName, (String)null, messageType, SERVICE_TYPE.DUBBO.getValue()));
    }

    public static MessageType getMessageType(String messageType) {
        return (MessageType)messageTypes.get(messageType);
    }

    public static void closeSession(String workNo) throws Exception {
        logger.info("Active Close Session, workNo=" + workNo);
        WebSocketSession session = null;
        if (relationMappings.containsKey(workNo)) {
            String sessionId = relationMappings.get(workNo);
            session = sessionRegistry.get(sessionId);
        } else if (sessionRegistry.containsKey(workNo)) {
            session = sessionRegistry.get(workNo);
        }

        if (session != null) {
            session.close(activeCloseStatus);
        }
    }

    public static WebSocketSession getSession(String workNo) throws Exception {
        WebSocketSession session = null;
        if (relationMappings.containsKey(workNo)) {
            String sessionId = relationMappings.get(workNo);
            session = sessionRegistry.get(sessionId);
        } else if (sessionRegistry.containsKey(workNo)) {
            session = sessionRegistry.get(workNo);
        }

        return session;
    }

    public static InetAddress getIP(String workNo) throws Exception {
        WebSocketSession session = getSession(workNo);
        return session == null ? null : session.getRemoteAddress().getAddress();
    }

    public static void updateSession(String workNo, WebSocketSession session) throws Exception {
        // Simply update the mapping without closing previous session
        relationMappings.put(workNo, session.getId());
    }

    public static void setWebSocketSessionListener(WebSocketSessionListener listener) {
        if (listener == null) {
            throw new NullPointerException("listener is null!");
        } else {
            webSocketSessionListener = listener;
        }
    }

    public static void setMessageFilter(MessageFilter messageFilter) {
        if (messageFilter == null) {
            throw new NullPointerException("messageFilter is null!");
        } else {
            SocketHandler.messageFilter = messageFilter;
        }
    }
}
